import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'pages/gobang_game_page.dart';
import 'rust/frb_generated.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await RustLib.init();
  runApp(const GobangApp());
}

class GobangApp extends StatelessWidget {
  const GobangApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1000, 900), // 调整设计尺寸，确保棋盘完整显示
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          title: '五子棋',
          theme: ThemeData(
            primarySwatch: Colors.brown,
            fontFamily: 'Microsoft YaHei', // 中文字体
            useMaterial3: true,
          ),
          home: const GobangGamePage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}
