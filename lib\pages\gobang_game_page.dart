import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/gobang_controller.dart';
import '../widgets/game_info_panel.dart';
import '../widgets/gobang_board.dart';

/// 五子棋游戏主页面
class GobangGamePage extends StatelessWidget {
  const GobangGamePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 初始化控制器
    Get.put(GobangController());

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC), // 米色背景
      appBar: AppBar(
        title: Text(
          '五子棋',
          style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF8B4513),
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 计算可用空间
            final availableWidth = constraints.maxWidth;
            final availableHeight = constraints.maxHeight;

            // 棋盘应该是正方形，使用高度作为棋盘尺寸
            final maxBoardSize = availableHeight;
            final spacing = 8.w;

            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 左侧棋盘区域 - 使用固定尺寸而不是Expanded
                SizedBox(width: maxBoardSize, height: maxBoardSize, child: const GobangBoard()),

                SizedBox(width: spacing),

                // 右侧信息面板区域 - 保持内容的自然宽度
                const IntrinsicWidth(child: GameInfoPanel()),
              ],
            );
          },
        ),
      ),
    );
  }
}

/// 游戏结束对话框
class GameOverDialog extends StatelessWidget {
  final String winner;
  final VoidCallback onRestart;
  final VoidCallback onClose;

  const GameOverDialog({super.key, required this.winner, required this.onRestart, required this.onClose});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5DC),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: const Color(0xFF8B4513), width: 2.w),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              '游戏结束',
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
            ),

            SizedBox(height: 16.h),

            // 获胜者信息
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: const Color(0xFFD2B48C)),
              ),
              child: Column(
                children: [
                  Icon(Icons.emoji_events, size: 48.w, color: Colors.amber),
                  SizedBox(height: 8.h),
                  Text(
                    winner,
                    style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: onRestart,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF8B4513),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
                    ),
                    child: Text('再来一局', style: TextStyle(fontSize: 14.sp)),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton(
                    onPressed: onClose,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF8B4513),
                      side: const BorderSide(color: Color(0xFF8B4513)),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
                    ),
                    child: Text('关闭', style: TextStyle(fontSize: 14.sp)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
